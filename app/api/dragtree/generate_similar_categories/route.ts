import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import {
  DragTreeStatus,
  DragTreeNodeStatus,
  // OpenAIUsageType, // Currently unused
} from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import { updateDragTree } from '@/app/server-actions/drag-tree'
import { generateDragTreeNodeId } from '@/app/(conv)/dragTree/[dragTreeId]/utils/id-generation'
import { DragtreeGenerateSimilarCategoriesRequestType } from '@/app/types/api'
import { streamText } from 'ai'
import { azure } from '@ai-sdk/azure'
import { getModelConfig } from '@/app/libs/model-config'
import {
  getLanguageName,
  getLLMLanguageName,
  type SupportedLanguageCode,
} from '@/app/(conv)/screening/constants/languages'
// import { logOpenAIUsage_serverAction } from '@/app/(legacy)/_server-actions/log_openai_usage' // Currently unused

export const maxDuration = 60

export async function POST(request: NextRequest) {
  try {
    console.log('🤖 [Similar Categories] Starting generation...')

    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      console.log('❌ [Similar Categories] Unauthorized - no session')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const requestData: DragtreeGenerateSimilarCategoriesRequestType =
      await request.json()
    const { nodeId, parentCategoryLabel } = requestData

    if (!nodeId || !parentCategoryLabel) {
      return NextResponse.json(
        { error: 'Node ID and parent category label are required' },
        { status: 400 }
      )
    }

    console.log(
      `🔍 [Similar Categories] Processing for node: ${nodeId} (${parentCategoryLabel})`
    )

    // Find the active drag tree for this user
    const activeDragTree = await prisma.dragTree.findFirst({
      where: {
        user_id: session.user.id,
        status: DragTreeStatus.ACTIVE,
      },
      orderBy: {
        updated_at: 'desc',
      },
    })

    if (!activeDragTree) {
      console.log('❌ [Similar Categories] No active drag tree found')
      return NextResponse.json(
        { error: 'No active drag tree found' },
        { status: 404 }
      )
    }

    console.log(`📋 [Similar Categories] Using tree: ${activeDragTree.id}`)

    /* 🚧 COMMENTED OUT FOR TESTING - RESTORE WHEN READY FOR PRODUCTION
    const systemMessage = constructSimilarCategoriesPrompt(
      parentCategoryLabel,
      existingSubcategories,
      originalContext,
      treeContext
    );

    // Get model configuration based on user's subscription tier
    const modelConfig = await getModelConfig(session.user.id, 'dragtree_generate_similar_categories')
    const model_name = modelConfig.model;

    try {
      const result = await streamText({
        model: azure(model_name),
        messages: [{ role: "system", content: systemMessage }],
        temperature: modelConfig.temperature,
        maxTokens: modelConfig.maxTokens,
        onFinish: async ({ text }) => {
          console.log(
            "api/dragtree/generate_similar_categories: completion:",
            text
          );
          await logOpenAIUsage_serverAction({
            open_ai_usage_type: OpenAIUsageType.DRAGTREE_GENERATE_QUESTIONS,
            model_name: model_name,
            input_text: systemMessage,
            output_text: text,
            userId: userId,
            conversationId: conversationId,
          });
        },
      });

      return result.toDataStreamResponse();
    } catch (error) {
      console.log("api/dragtree/generate_similar_categories: error:", error);
      return new NextResponse("Error", { status: 500 });
    }
    */

    // Generate content (static for now)
    console.log('🧪 [Similar Categories] Generating static content')
    await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate delay

    const generatedMarkdown = `# ${parentCategoryLabel}
## (testing) category 1 ${parentCategoryLabel}
### (testing) question 1 ${parentCategoryLabel}
### (testing) question 2 ${parentCategoryLabel}
## (testing) category 2 ${parentCategoryLabel}
### (testing) question 3 ${parentCategoryLabel}
### (testing) question 4 ${parentCategoryLabel}`

    console.log(
      '📝 [Similar Categories] Generated markdown:',
      generatedMarkdown
    )

    // Parse markdown into structured data using two-pass approach for flexibility
    const lines = generatedMarkdown.split('\n').filter(line => line.trim())

    // First pass: Parse all headings and build hierarchy (treat all as categories initially)
    const tempNodes: Array<{
      id: string
      drag_tree_id: string
      label: string
      level: number
      parentId: string
      markdownLevel: number
    }> = []

    // Stack to track current hierarchy, similar to markdownToTreeNode.ts
    const stack: { level: number; nodeId: string; markdownLevel: number }[] = []

    // Use the provided nodeId directly instead of generating from label
    const parentNodeId = nodeId

    // Get the parent node to determine the level
    const parentNode = await prisma.dragTreeNode.findUnique({
      where: { id: parentNodeId },
    })

    if (!parentNode) {
      console.log('❌ [Similar Categories] Parent category not found')
      return NextResponse.json(
        { error: 'Parent category not found' },
        { status: 404 }
      )
    }

    const parentLevel = (parentNode.metadata as any)?.level || 0

    // First pass: Parse all headings and build temporary structure
    for (const line of lines) {
      const trimmed = line.trim()

      // Skip root heading (# ParentCategory)
      if (trimmed.startsWith('# ')) continue

      // Parse any heading level (##, ###, ####, etc.)
      const headingMatch = trimmed.match(/^(#{2,})\s+(.+)$/)
      if (headingMatch) {
        const markdownLevel = headingMatch[1].length // Number of # symbols
        const label = headingMatch[2]

        // Calculate actual level based on parent
        const actualLevel = parentLevel + (markdownLevel - 1) // -1 because ## is first level after parent

        // Use deterministic ID generation with timestamp to ensure uniqueness on regeneration
        const timestamp = Date.now()
        const newNodeId = generateDragTreeNodeId(
          activeDragTree.id,
          `${label}_gen_${timestamp}`,
          'CATEGORY' // Will be corrected to QUESTION in second pass if needed
        )

        // Find correct parent based on stack (similar to markdownToTreeNode.ts)
        while (
          stack.length > 0 &&
          stack[stack.length - 1].markdownLevel >= markdownLevel
        ) {
          stack.pop()
        }

        let currentParentId = parentNodeId // Default to original parent
        if (stack.length > 0) {
          currentParentId = stack[stack.length - 1].nodeId
        }

        // Store temporary node data
        tempNodes.push({
          id: newNodeId,
          drag_tree_id: activeDragTree.id,
          label: label,
          level: actualLevel,
          parentId: currentParentId,
          markdownLevel: markdownLevel,
        })

        // Push to stack for future children
        stack.push({
          level: actualLevel,
          nodeId: newNodeId,
          markdownLevel: markdownLevel,
        })

        console.log(
          `📋 [Similar Categories] Parsed heading: "${label}" (level ${actualLevel}, parent: ${currentParentId})`
        )
      }
    }

    // Second pass: Determine which nodes are leaf nodes (questions) vs categories
    const childrenMap = new Map<string, string[]>()
    const tempIdToFinalId = new Map<string, string>() // Track temp ID to final ID mapping

    // Build children map
    tempNodes.forEach(node => {
      if (!childrenMap.has(node.parentId)) {
        childrenMap.set(node.parentId, [])
      }
      childrenMap.get(node.parentId)!.push(node.id)
    })

    // Generate final IDs for all nodes
    const generationTimestamp = Date.now() // Single timestamp for this generation batch
    tempNodes.forEach(tempNode => {
      const hasChildren =
        childrenMap.has(tempNode.id) && childrenMap.get(tempNode.id)!.length > 0
      const nodeType = hasChildren ? 'CATEGORY' : 'QUESTION'

      // Generate fresh ID with proper type and timestamp for uniqueness
      let finalNodeId = tempNode.id // Keep category ID as-is
      if (nodeType === 'QUESTION') {
        // Generate new ID with correct QUESTION type and timestamp
        finalNodeId = generateDragTreeNodeId(
          activeDragTree.id,
          `${tempNode.label}_gen_${generationTimestamp}`,
          'QUESTION'
        )
      }

      tempIdToFinalId.set(tempNode.id, finalNodeId)
    })

    // Create final nodes with correct types
    const newNodes: Array<{
      id: string
      drag_tree_id: string
      node_type: 'CATEGORY' | 'QUESTION'
      label: string
      metadata: any
      status: DragTreeNodeStatus
    }> = []

    const newCategoryIds: string[] = []
    const hierarchyUpdates: Record<string, string[]> = {}

    tempNodes.forEach(tempNode => {
      const hasChildren =
        childrenMap.has(tempNode.id) && childrenMap.get(tempNode.id)!.length > 0
      const nodeType = hasChildren ? 'CATEGORY' : 'QUESTION'
      const finalNodeId = tempIdToFinalId.get(tempNode.id)!

      // Create the final node
      newNodes.push({
        id: finalNodeId,
        drag_tree_id: activeDragTree.id,
        node_type: nodeType,
        label: tempNode.label,
        metadata: {
          generatedType: 'similar_categories',
          level: tempNode.level,
          parentId: tempNode.parentId,
        },
        status: DragTreeNodeStatus.ACTIVE,
      })

      // Track categories for later hierarchy building
      if (nodeType === 'CATEGORY') {
        newCategoryIds.push(finalNodeId)
      }

      // Only add internal relationships to hierarchyUpdates (not parent relationships)
      if (nodeType === 'CATEGORY') {
        hierarchyUpdates[finalNodeId] = []
      }

      console.log(
        `✅ [Similar Categories] Final ${nodeType}: "${tempNode.label}" (level ${tempNode.level})`
      )
    })

    // Build internal hierarchy relationships (children of new categories)
    tempNodes.forEach(tempNode => {
      const children = childrenMap.get(tempNode.id) || []
      if (children.length > 0) {
        const finalNodeId = tempIdToFinalId.get(tempNode.id)!
        // Map temp IDs to final IDs using our mapping
        const finalChildren = children.map(childTempId => {
          return tempIdToFinalId.get(childTempId)!
        })

        hierarchyUpdates[finalNodeId] = finalChildren
      }
    })

    // Get current tree structure and update it
    const currentTreeStructure = activeDragTree.tree_structure as {
      root_id: string
      hierarchy: Record<string, string[]>
    }

    // Update hierarchy to include new categories under the parent (append, don't replace)
    const updatedHierarchy = { ...currentTreeStructure.hierarchy }
    if (!updatedHierarchy[parentNodeId]) {
      updatedHierarchy[parentNodeId] = []
    }

    // Append new category IDs to existing children
    updatedHierarchy[parentNodeId] = [
      ...updatedHierarchy[parentNodeId],
      ...newCategoryIds,
    ]

    // Add the internal hierarchy relationships of new categories (don't overwrite parent)
    Object.keys(hierarchyUpdates).forEach(nodeId => {
      if (nodeId !== parentNodeId) {
        // Skip parent node to avoid overwriting
        updatedHierarchy[nodeId] = hierarchyUpdates[nodeId]
      }
    })

    const updatedTreeStructure = {
      root_id: currentTreeStructure.root_id,
      hierarchy: updatedHierarchy,
    }

    console.log(
      `💾 [Similar Categories] Updating database with ${newNodes.length} new nodes`
    )

    // Atomic database update using Promise.all
    await Promise.all([
      // Create new nodes
      prisma.dragTreeNode.createMany({
        data: newNodes,
        skipDuplicates: true,
      }),
      // Update tree structure
      updateDragTree({
        treeId: activeDragTree.id,
        treeStructure: updatedTreeStructure,
      }),
    ])

    console.log(
      '🎉 [Similar Categories] Database update completed successfully'
    )

    // Return simple success response - FE will refresh from database
    return NextResponse.json({
      success: true,
      message: 'Similar categories generated successfully',
      shouldRefresh: true,
      nodeCount: newNodes.length,
    })
  } catch (error) {
    console.error('💥 [Similar Categories] Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/* Currently unused function - uncomment when needed
const constructSimilarCategoriesPrompt = (
  parentCategoryLabel: string,
  existingSubcategories: string[],
  originalContext: string,
  treeContext: string,
  preferredLanguage: SupportedLanguageCode = 'en'
): string => {
  const existingSubcategoriesText =
    existingSubcategories.length > 0
      ? existingSubcategories.map(cat => `- ${cat}`).join('\n')
      : 'No existing subcategories yet.'

  const languageName = getLLMLanguageName(preferredLanguage)

  return `As an elite consultant, you are tasked with generating a focused subtree for a specific category in our project structure.

**Parent Category**: ${parentCategoryLabel}

**Project Context**: ${originalContext}

**Current Tree Structure for Reference**:
${treeContext}

**Existing Subcategories Under "${parentCategoryLabel}"**:
${existingSubcategoriesText}

Please generate a mini tree structure that:
1. Creates 2-3 new subcategories and questions under "${parentCategoryLabel}"
2. Avoids duplicating existing subcategories
3. Focuses on unexplored aspects of the parent category
4. Maintains consistency with the existing tree structure
5. Emphasizes WHY and WHAT questions primarily
6. Can go multiple levels deep as needed - be flexible with the structure

**Format Requirements**:
- Start with "# ${parentCategoryLabel}" as the root heading
- Use markdown headings (##, ###, ####, etc.) flexibly based on what makes sense
- For questions, use "### " format only
- Do NOT include examples or explanations after questions
- Be creative and flexible with the structure - don't limit yourself to rigid levels

**Example Output Format**:
# ${parentCategoryLabel}
## New Subcategory 1
### What are the key considerations for this aspect?
### Why is this element crucial for success?
### How does this impact the overall timeline?
## New Subcategory 2
### What components are needed for this approach?
### How does this integrate with existing systems?
### What are the potential risks and mitigation strategies?

Think creatively about the structure and depth. Generate a focused subtree that expands the "${parentCategoryLabel}" category meaningfully.

Please generate all content in ${languageName}.`
}
*/
