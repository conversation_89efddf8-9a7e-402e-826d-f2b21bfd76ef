/**
 * Tests for FlowExportButton component permission behavior
 * Ensures correct UI states based on tier permissions
 */

import React from 'react'
import { render, screen } from '@testing-library/react'
import { SubscriptionTier } from '@prisma/client'
import { FlowExportButton } from '@/app/(conv)/dragTree/[dragTreeId]/components/VisualFlowDiagram/components/FlowExportButton'

// Mock next-auth
const mockSession = (tier: SubscriptionTier) => ({
  data: { user: { subscription: { tier } } },
})

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}))

// Mock the tab store
jest.mock('@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore', () => ({
  useTabStore: jest.fn(() => ({
    activeTabId: 'main',
    setActiveTab: jest.fn(),
  })),
}))

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

const { useSession } = require('next-auth/react')

describe('FlowExportButton Permission Tests', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(() => null),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    })
  })

  describe('Tier-based Export Permissions', () => {
    it('should show "Export not available" for VIEWER tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.VIEWER))

      render(<FlowExportButton />)

      // Both buttons should show "Export not available"
      const buttons = screen.getAllByText('Export not available')
      expect(buttons).toHaveLength(2)

      // Buttons should be disabled
      buttons.forEach(button => {
        expect(button.closest('button')).toBeDisabled()
      })
    })

    it('should show "Upgrade to unlock" for FREE tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.FREE))

      render(<FlowExportButton />)

      // Both buttons should show "Upgrade to unlock"
      const buttons = screen.getAllByText('Upgrade to unlock')
      expect(buttons).toHaveLength(2)

      // Buttons should be disabled
      buttons.forEach(button => {
        expect(button.closest('button')).toBeDisabled()
      })
    })

    it('should show "Export not available" for DUMMY tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.DUMMY))

      render(<FlowExportButton />)

      // Both buttons should show "Export not available"
      const buttons = screen.getAllByText('Export not available')
      expect(buttons).toHaveLength(2)

      // Buttons should be disabled
      buttons.forEach(button => {
        expect(button.closest('button')).toBeDisabled()
      })
    })

    it('should show proper export labels for PRO tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.PRO))

      render(<FlowExportButton />)

      // Should show proper export button labels
      expect(screen.getByText('Download Hierarchical Flow')).toBeInTheDocument()
      expect(screen.getByText('Download Circular Flow')).toBeInTheDocument()

      // Buttons should be enabled (not disabled due to permissions)
      const hierarchicalButton = screen
        .getByText('Download Hierarchical Flow')
        .closest('button')
      const circularButton = screen
        .getByText('Download Circular Flow')
        .closest('button')

      // Note: buttons might still be disabled due to other conditions (like diagram not ready)
      // but not due to permissions
      expect(hierarchicalButton).toBeInTheDocument()
      expect(circularButton).toBeInTheDocument()
    })

    it('should show proper export labels for GUEST tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.GUEST))

      render(<FlowExportButton />)

      expect(screen.getByText('Download Hierarchical Flow')).toBeInTheDocument()
      expect(screen.getByText('Download Circular Flow')).toBeInTheDocument()
    })

    it('should show proper export labels for ULTRA tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.ULTRA))

      render(<FlowExportButton />)

      expect(screen.getByText('Download Hierarchical Flow')).toBeInTheDocument()
      expect(screen.getByText('Download Circular Flow')).toBeInTheDocument()
    })

    it('should show proper export labels for BUSINESS tier', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.BUSINESS))

      render(<FlowExportButton />)

      expect(screen.getByText('Download Hierarchical Flow')).toBeInTheDocument()
      expect(screen.getByText('Download Circular Flow')).toBeInTheDocument()
    })
  })

  describe('Upgrade Notices', () => {
    it('should show "Upgrade to unlock" only for FREE tier and render an upgrade button', () => {
      const tiers = [
        SubscriptionTier.VIEWER,
        SubscriptionTier.DUMMY,
        SubscriptionTier.PRO,
        SubscriptionTier.GUEST,
        SubscriptionTier.ULTRA,
        SubscriptionTier.BUSINESS,
      ]

      // FREE shows upgrade
      useSession.mockReturnValue(mockSession(SubscriptionTier.FREE))
      let { unmount, container } = render(<FlowExportButton />)
      expect(screen.getAllByText('Upgrade to unlock')).toHaveLength(2)
      // Upgrade notice/button should render
      expect(
        container.querySelector('a[href="/subscription"]')
      ).toBeInTheDocument()
      unmount()

      // Others do not show upgrade string
      tiers.forEach(tier => {
        useSession.mockReturnValue(mockSession(tier))
        const utils = render(<FlowExportButton />)
        expect(screen.queryByText(/upgrade to unlock/i)).not.toBeInTheDocument()
        utils.unmount()
      })
    })

    it('should not render unrelated UpgradeNotice components', () => {
      useSession.mockReturnValue(mockSession(SubscriptionTier.VIEWER))

      const { container } = render(<FlowExportButton />)

      // Should not contain any elements with upgrade notice classes
      expect(container.querySelector('.upgrade-notice')).not.toBeInTheDocument()
      expect(
        container.querySelector('[class*="upgrade-notice"]')
      ).not.toBeInTheDocument()
    })
  })

  describe('Button State Consistency', () => {
    it('should have consistent disabled state for restricted tiers', () => {
      const restrictedTiers = [
        SubscriptionTier.VIEWER,
        SubscriptionTier.FREE,
        SubscriptionTier.DUMMY,
      ]

      restrictedTiers.forEach(tier => {
        useSession.mockReturnValue(mockSession(tier))

        const { unmount } = render(<FlowExportButton />)

        const buttons = screen.getAllByRole('button')
        expect(buttons).toHaveLength(2)

        // Both buttons should be disabled for restricted tiers
        buttons.forEach(button => {
          expect(button).toBeDisabled()
        })

        unmount()
      })
    })

    it('should have proper button structure for allowed tiers', () => {
      const allowedTiers = [
        SubscriptionTier.PRO,
        SubscriptionTier.GUEST,
        SubscriptionTier.ULTRA,
        SubscriptionTier.BUSINESS,
      ]

      allowedTiers.forEach(tier => {
        useSession.mockReturnValue(mockSession(tier))

        const { unmount } = render(<FlowExportButton />)

        const buttons = screen.getAllByRole('button')
        expect(buttons).toHaveLength(2)

        // Buttons should exist and have proper structure
        buttons.forEach(button => {
          expect(button).toBeInTheDocument()
          expect(button).toHaveClass('w-full', 'justify-start')
        })

        unmount()
      })
    })
  })

  describe('Tier Override Support', () => {
    it('no longer supports localStorage tier override (uses session/DB tier only)', () => {
      // Mock localStorage override
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: jest.fn(() => SubscriptionTier.PRO),
          setItem: jest.fn(),
          removeItem: jest.fn(),
          clear: jest.fn(),
        },
        writable: true,
      })

      // Session is VIEWER, component should remain restricted
      useSession.mockReturnValue(mockSession(SubscriptionTier.VIEWER))

      render(<FlowExportButton />)

      const buttons = screen.getAllByText('Export not available')
      expect(buttons).toHaveLength(2)
    })
  })

  describe('Accessibility', () => {
    it('should have proper button accessibility for all tiers', () => {
      Object.values(SubscriptionTier).forEach(tier => {
        useSession.mockReturnValue(mockSession(tier))

        const { unmount } = render(<FlowExportButton />)

        const buttons = screen.getAllByRole('button')
        expect(buttons).toHaveLength(2)

        buttons.forEach(button => {
          if (tier === SubscriptionTier.FREE) {
            expect(button).toHaveTextContent(/upgrade to unlock/i)
          } else {
            expect(button).toHaveTextContent(/download|export/i)
          }
        })

        unmount()
      })
    })
  })
})
