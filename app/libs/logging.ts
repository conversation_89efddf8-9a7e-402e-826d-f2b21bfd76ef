/**
 * Centralized Logging Utility System
 *
 * This utility provides a unified interface for logging events across different services.
 * It supports PostHog analytics integration and has an extensible architecture for future services.
 *
 * Features:
 * - PostHog integration as primary service
 * - Extensible architecture for future services (memory logging to database)
 * - Feature flags to control which logging services are active
 * - Non-blocking operation to prevent impact on user experience
 * - Proper error handling and fallbacks
 */

import {
  trackEvent as posthogTrackEvent,
  identifyUser as posthogIdentifyUser,
} from '@/app/libs/posthog'
import {
  EventName,
  LOGGING_FLAGS,
  isValidEventName,
  getEventMetadata,
} from '@/app/configs/logging-config'

// Re-export types for external use
export type { EventName } from '@/app/configs/logging-config'
import { validateEventProperties } from '@/app/libs/logging-validation'

// ============================================================================
// TYPESCRIPT-ENFORCED EVENT PROPERTIES
// ============================================================================

// Define exact property types for each event
export type EventPropertyTypes = {
  // Screening Module Events
  click_screening_paraphraseAndAnalyze: {
    description_length: number
    selected_language: string
    use_simulator: boolean
  }
  click_screening_startClarification: {
    description_length: number
    selected_suggestion: string
    selected_language: string
  }

  // DragTree Module Events
  click_outline_quickResearch: {
    question_id: string
    question_text: string
    research_option: string
    research_label: string
  }
  click_reactflow_quickResearch: {
    question_id: string
    question_text: string
    research_option: string
    research_label: string
  }
  click_reactflow_hierarchical: {
    previous_mode: string
    new_mode: string
  }
  click_reactflow_circular: {
    previous_mode: string
    new_mode: string
  }
  edit_outline_title: {
    old_title: string
    new_title: string
    title_length: number
  }
  click_dragtree_batchResearch: {
    question_ids: string[]
    question_texts: string[]
    batch_count: number
    tier: string
    effective_limit: number
  }
  click_settings_copyOriginal: {
    content_length: number
    copy_format: string
  }
  click_settings_copyMarkdown: {
    content_length: number
    copy_format: string
  }

  // AI Pane Module Events
  click_aipane_startGenerate: {
    prompt_length: number
    context_count: number
    total_tokens: number
    settings_type: string
  }
  click_aipane_copyGenerated: {
    content_length: number
    generation_state: string
    asset_id: string
  }
  click_aipane_startChat: {
    context_count: number
    initial_message_length: number
    total_tokens: number
    settings_type: string
  }
  click_aipane_sendMessage: {
    message_length: number
    conversation_id: string
    context_count: number
  }

  // Subscription Module Events
  click_upgrade_sidebar: {
    source: string
    current_tier: string
  }
  click_manage_subscription_profile: {
    current_tier: string | undefined
    customer_id: string | undefined
  }
  click_subscribe_profile: {
    current_tier: string | undefined
  }

  // Feedback Module Events
  click_feedback_widget_opened: {
    // No additional properties required
  }
  submit_feedback_widget: {
    feedbackType: string | null
    engagementLevel: string | null
    hasRatings: boolean
    feedbackLength: number
    feedbackRecordId: string | undefined
  }
}

// ============================================================================
// TYPES
// ============================================================================

export type LoggingContext = {
  userId?: string
  dragTreeId?: string
  sessionId?: string
  timestamp?: string
  userAgent?: string
  url?: string
}

export type EventProperties = Record<string, any>

export type LoggingService = 'posthog' | 'memory'

// ============================================================================
// MEMORY LOGGING INTERFACE (STUBBED)
// ============================================================================

/**
 * Memory logging service - stores events for future database persistence
 * Currently stubbed out - will be implemented when database logging is needed
 */
class MemoryLoggingService {
  private events: Array<{
    eventName: EventName
    properties: EventProperties
    context: LoggingContext
    timestamp: Date
  }> = []

  async logEvent(
    eventName: EventName,
    properties: EventProperties,
    context: LoggingContext
  ): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[MemoryLogging] ${eventName}:`, { properties, context })
    }

    // // Store event in memory for future database persistence
    // this.events.push({
    //   eventName,
    //   properties,
    //   context,
    //   timestamp: new Date(),
    // })

    // TODO: Implement database persistence
    // This could batch events and periodically flush to database
  }

  async identifyUser(
    userId: string,
    properties?: EventProperties
  ): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[MemoryLogging] Identify user: ${userId}`, properties)
    }
    // TODO: Implement user identification storage
  }

  getStoredEvents() {
    return [...this.events]
  }

  clearStoredEvents() {
    this.events = []
  }
}

// ============================================================================
// POSTHOG LOGGING SERVICE
// ============================================================================

/**
 * PostHog logging service - integrates with existing PostHog setup
 */
class PostHogLoggingService {
  async logEvent(
    eventName: EventName,
    properties: EventProperties,
    context: LoggingContext
  ): Promise<void> {
    try {
      const enrichedProperties = {
        ...properties,
        ...context,
        event_category: getEventMetadata(eventName).category,
        timestamp: new Date().toISOString(),
      }

      posthogTrackEvent(eventName, enrichedProperties)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error(
          `[PostHogLogging] Failed to log event ${eventName}:`,
          error
        )
      }
    }
  }

  async identifyUser(
    userId: string,
    properties?: EventProperties
  ): Promise<void> {
    try {
      posthogIdentifyUser(userId, properties)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[PostHogLogging] Failed to identify user:', error)
      }
    }
  }
}

// ============================================================================
// TYPE-SAFE LOGGING FUNCTIONS
// ============================================================================

/**
 * Type-safe logging function with full TypeScript validation
 * Enforces correct properties at compile time for each event
 */
export function logEventWithContext<T extends EventName>(
  eventName: T,
  userId: string | undefined,
  dragTreeId: string | undefined,
  properties: EventPropertyTypes[T]
): void {
  // Call the main logging utility with validated properties
  loggingUtility.logEvent(eventName, properties as EventProperties, {
    userId,
    dragTreeId,
  })
}

// ============================================================================
// MAIN LOGGING UTILITY
// ============================================================================

class LoggingUtility {
  private posthogService = new PostHogLoggingService()
  private memoryService = new MemoryLoggingService()

  /**
   * Log an event across all enabled services
   */
  async logEvent(
    eventName: EventName,
    properties: EventProperties = {},
    context: LoggingContext = {}
  ): Promise<void> {
    // Validate event name
    if (!isValidEventName(eventName)) {
      if (process.env.NODE_ENV === 'development') {
        console.error(`[Logging] Invalid event name: ${eventName}`)
      }
      return
    }

    // Comprehensive validation with warnings for missing optional properties
    const validation = validateEventProperties(eventName, properties, context)

    // Log validation results in development
    if (process.env.NODE_ENV === 'development') {
      validation.errors.forEach(error => console.error(`[Logging] ${error}`))
      validation.warnings.forEach(warning =>
        console.warn(`[Logging] ${warning}`)
      )
    }

    // Continue with logging even if validation has warnings (non-blocking)
    const metadata = getEventMetadata(eventName)

    // Add metadata to properties
    const enrichedProperties = {
      ...properties,
      event_label: metadata.label,
      event_description: metadata.description,
    }

    // Log to enabled services (non-blocking)
    const promises: Promise<void>[] = []

    if (LOGGING_FLAGS.logPosthog) {
      promises.push(
        this.posthogService.logEvent(eventName, enrichedProperties, context)
      )
    }

    if (LOGGING_FLAGS.logMemory) {
      promises.push(
        this.memoryService.logEvent(eventName, enrichedProperties, context)
      )
    }

    // Execute all logging operations without blocking
    Promise.allSettled(promises).catch(error => {
      if (process.env.NODE_ENV === 'development') {
        console.error('[Logging] Some logging services failed:', error)
      }
    })
  }

  /**
   * Identify a user across all enabled services
   */
  async identifyUser(
    userId: string,
    properties: EventProperties = {}
  ): Promise<void> {
    const promises: Promise<void>[] = []

    if (LOGGING_FLAGS.logPosthog) {
      promises.push(this.posthogService.identifyUser(userId, properties))
    }

    if (LOGGING_FLAGS.logMemory) {
      promises.push(this.memoryService.identifyUser(userId, properties))
    }

    // Execute all identification operations without blocking
    Promise.allSettled(promises).catch(error => {
      if (process.env.NODE_ENV === 'development') {
        console.error('[Logging] Some user identification failed:', error)
      }
    })
  }

  /**
   * Get stored events from memory service (for debugging/testing)
   */
  getStoredEvents() {
    return this.memoryService.getStoredEvents()
  }

  /**
   * Clear stored events from memory service
   */
  clearStoredEvents() {
    this.memoryService.clearStoredEvents()
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

const loggingUtility = new LoggingUtility()

// ============================================================================
// EXPORTED FUNCTIONS
// ============================================================================

/**
 * Log an event with the centralized logging system
 */
export const logEvent = (
  eventName: EventName,
  properties?: EventProperties,
  context?: LoggingContext
) => {
  loggingUtility.logEvent(eventName, properties, context)
}

/**
 * Identify a user with the centralized logging system
 */
export const identifyUser = (userId: string, properties?: EventProperties) => {
  loggingUtility.identifyUser(userId, properties)
}

/**
 * Get stored events (for debugging/testing)
 */
export const getStoredEvents = () => {
  return loggingUtility.getStoredEvents()
}

/**
 * Clear stored events
 */
export const clearStoredEvents = () => {
  loggingUtility.clearStoredEvents()
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Create a logging context from common sources
 */
export const createLoggingContext = (
  userId?: string,
  dragTreeId?: string,
  additionalContext?: Partial<LoggingContext>
): LoggingContext => {
  return {
    userId,
    dragTreeId,
    sessionId:
      typeof window !== 'undefined'
        ? window.sessionStorage?.getItem('sessionId') || undefined
        : undefined,
    timestamp: new Date().toISOString(),
    userAgent:
      typeof window !== 'undefined' ? window.navigator?.userAgent : undefined,
    url: typeof window !== 'undefined' ? window.location?.href : undefined,
    ...additionalContext,
  }
}

// Types are already exported above, no need to re-export
