# Centralized Logging System

This directory contains the centralized logging system for the application, designed to replace scattered analytics implementations (like Mixpanel) with a unified, type-safe approach.

## 📁 File Structure

```
app/libs/
├── logging.ts              # Main logging utility and exported functions
├── logging-validation.ts   # Event validation logic
├── posthog.ts              # PostHog integration
└── logging-providers/      # Provider implementations (deprecated/stub files)
```

## 🎯 Quick Start

### 1. Import the logging function

```typescript
import { logEventWithContext } from '@/app/libs/logging'
import { useSession } from 'next-auth/react'
```

### 2. Log events in your component

```typescript
const { data: session } = useSession()

// Example: Log a button click
logEventWithContext(
  'click_aipane_startGenerate', // Event name (must be defined in config)
  session?.user?.id, // User ID (optional)
  dragTreeId, // DragTree ID (optional)
  {
    // Event properties
    prompt_length: prompt.length,
    context_count: selectedItems.length,
    settings_type: 'generate',
  }
)
```

## 📋 Adding New Events

### Step 1: Add Event Name to Type Union

In `app/configs/logging-config.ts`, add your event to the `EventName` type:

```typescript
export type EventName =
  // ... existing events
  'click_your_new_event' | 'submit_your_form'
```

### Step 2: Add Event Definition

Add the event metadata to `EVENT_DEFINITIONS`:

```typescript
export const EVENT_DEFINITIONS: Record<EventName, EventMetadata> = {
  // ... existing events

  click_your_new_event: {
    name: 'click_your_new_event',
    label: 'Your Feature: New Event Click',
    description: 'User clicked the new event button',
    category: EVENT_CATEGORIES.YOUR_CATEGORY, // Use existing or add new category
    requiresUserId: true, // Whether userId is required
    requiresDragTreeId: false, // Whether dragTreeId is required
    optionalProperties: [
      // Properties that may be included
      'button_text',
      'context_data',
      'user_tier',
    ],
  },
}
```

### Step 3: Add Category (if needed)

If using a new category, add it to `EVENT_CATEGORIES`:

```typescript
export const EVENT_CATEGORIES = {
  // ... existing categories
  YOUR_CATEGORY: 'your_category',
} as const
```

## 🔧 Event Naming Convention

Follow the pattern: `[verb]_[entity]_[feature]`

**Examples:**

- `click_aipane_startGenerate` - User clicked start generate in AI pane
- `submit_feedback_widget` - User submitted feedback widget
- `edit_outline_title` - User edited title in outline
- `click_upgrade_sidebar` - User clicked upgrade in sidebar

## 📊 Current Event Categories

- **screening** - Screening module interactions
- **dragtree** - DragTree and outline interactions
- **aipane** - AI pane (chat/generate) interactions
- **settings** - Settings and configuration changes
- **subscription** - Subscription and billing interactions
- **feedback** - Feedback collection and surveys

## 🛠 Configuration

### Logging Flags

Control which services are active in `app/configs/logging-config.ts`:

```typescript
export const LOGGING_FLAGS = {
  logPosthog: true, // PostHog analytics (production)
  logMemory: false, // Memory logging (development/testing)
}
```

### Provider Configuration

The system supports multiple providers but currently uses PostHog:

```typescript
export const ACTIVE_PROVIDER: AnalyticsProvider = 'posthog'
```

## 🧪 Testing

The logging system includes comprehensive validation:

1. **AST Validation** - Ensures all defined events are actually called
2. **Property Validation** - Validates required/optional properties at runtime
3. **Type Safety** - Full TypeScript validation for event properties

Run tests: `npm run test -- --testNamePattern="logging"`

## 🚀 Migration from Mixpanel

### Replace Mixpanel calls:

```typescript
// OLD: Mixpanel
mixpanel.track('feedback_widget_submitted', {
  dragTreeId,
  feedbackType: selectedType,
})

// NEW: Centralized Logging
logEventWithContext('submit_feedback_widget', session?.user?.id, dragTreeId, {
  feedbackType: selectedType,
})
```

### Benefits of Migration:

- **Type Safety** - Compile-time validation of event names and properties
- **Centralized Config** - All events defined in one place
- **Provider Flexibility** - Easy to switch between PostHog, Amplitude, etc.
- **Validation** - Runtime validation with helpful error messages
- **Testing** - Comprehensive test coverage ensures events are actually called

## 🔍 Debugging

### Development Mode

In development, the system logs validation warnings and errors to console:

```
[Logging] Warning: Optional property 'user_tier' missing for event 'click_upgrade_sidebar'
[Logging] ✅ Event logged: click_aipane_startGenerate
```

### Memory Service

Enable memory logging for debugging:

```typescript
// In logging-config.ts
export const LOGGING_FLAGS = {
  logPosthog: true,
  logMemory: true, // Enable for debugging
}

// Access stored events
import { getStoredEvents } from '@/app/libs/logging'
console.log(getStoredEvents())
```

## 📝 Future AI Model Instructions

When adding new tracking events:

1. **Always use the centralized logging system** - Never add direct provider calls
2. **Follow naming conventions** - Use `[verb]_[entity]_[feature]` pattern
3. **Add proper validation** - Define required/optional properties
4. **Test thoroughly** - Run AST validation tests to ensure events are called
5. **Update documentation** - Add examples for complex event patterns

The system is designed to be self-documenting through the configuration file and validation tests.

## ⚠️ Migration Status

**Current Status**: Partial migration from Mixpanel

- ✅ **Feedback Widget** - Migrated to centralized logging
- ❌ **Legacy Components** - Still using Mixpanel (in `(legacy)` folders)
- ❌ **Active Components** - ~20 active Mixpanel usages remain in non-legacy code

**Next Steps for Complete Migration**:

1. Replace remaining `mixpanel.track()` calls with `logEventWithContext()`
2. Add missing events to `logging-config.ts`
3. Remove Mixpanel dependency from `package.json`
4. Clean up unused Mixpanel import statements
